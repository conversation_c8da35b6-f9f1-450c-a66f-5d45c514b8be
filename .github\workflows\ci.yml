name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Set up Python ${{ matrix.python-version }}
      run: uv python install ${{ matrix.python-version }}

    - name: Install dependencies
      run: uv sync --all-extras

    - name: Run linting with ruff
      run: |
        uv run ruff check src/ tests/
        uv run ruff format --check src/ tests/

    - name: Run type checking with mypy
      run: uv run mypy src/ || true

    - name: Run tests with pytest and coverage
      run: |
        uv run pytest tests/ -v

    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: coverage-report
        path: htmlcov/

    - name: Test CLI commands (dry run)
      run: |
        uv run banana-forge --help
        uv run banana-forge version
        uv run banana-forge config
        uv run banana-forge generate "Test feature" --dry-run

  build:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: uv sync

    - name: Build package
      run: uv build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/

  integration-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: uv sync

    - name: Run integration tests (if API key available)
      env:
        OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
      run: |
        if [ -n "$OPENROUTER_API_KEY" ]; then
          echo "Running integration tests with real API..."
          uv run pytest tests/integration/ -v || echo "Integration tests failed or not found"
        else
          echo "Skipping integration tests - no API key configured"
        fi

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Install dependencies
      run: uv sync

    - name: Run security checks
      run: |
        uv run pip install bandit
        echo "Running bandit security scan..."
        uv run bandit -r src/ -f json -o bandit-report.json || true
        echo "Security scan completed"

    - name: Upload security report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-report
        path: bandit-report.json
