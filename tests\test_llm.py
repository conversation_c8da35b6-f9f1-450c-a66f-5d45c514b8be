"""
Tests for the LLM client module.
"""

from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import httpx
import pytest

from banana_forge.llm import LLMClient


class TestLLMClient:
    """Test cases for LLMClient."""

    def test_llm_client_initialization(self):
        """Test LLM client initialization."""
        with patch.object(LLMClient, "_setup_clients"):
            client = LLMClient()
            assert client is not None

    def test_is_local_model(self):
        """Test local model detection."""
        with patch.object(LLMClient, "_setup_clients"):
            client = LLMClient()

            # Test local models
            assert client.is_local_model("qwen2.5:8b") is True
            assert client.is_local_model("llama2") is True
            assert client.is_local_model("mistral") is True

            # Test remote models
            assert client.is_local_model("openai/gpt-4") is False
            assert client.is_local_model("anthropic/claude-3") is False
            assert client.is_local_model("moonshot/kimi-k2") is False

    def test_setup_clients(self):
        """Test client setup."""
        with patch("banana_forge.llm.httpx.Client") as mock_httpx:
            with patch("banana_forge.llm.settings") as mock_settings:
                mock_client = MagicMock()
                mock_httpx.return_value = mock_client

                # Mock settings to provide API key
                mock_settings.openrouter_api_key = "test-api-key"
                mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
                mock_settings.ollama_base_url = "http://localhost:11434"
                mock_settings.verbose = False

                client = LLMClient()

                # Should have created clients
                assert client.is_openrouter_available() is True
                assert client.is_ollama_available() is True

    def test_generate_completion_openrouter(self):
        """Test completion generation with OpenRouter."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_openrouter_completion") as mock_gen:
                mock_gen.return_value = "Test response"

                client = LLMClient()

                result = client.generate_completion(
                    prompt="Test prompt", model="openai/gpt-4", max_tokens=100
                )

                assert result == "Test response"
                mock_gen.assert_called_once()

    def test_generate_completion_ollama(self):
        """Test completion generation with Ollama."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_ollama_completion") as mock_gen:
                mock_gen.return_value = "Test response from Ollama"

                client = LLMClient()

                result = client.generate_completion(
                    prompt="Test prompt", model="qwen2.5:8b", max_tokens=100
                )

                assert result == "Test response from Ollama"
                mock_gen.assert_called_once()

    @patch("banana_forge.llm.httpx.Client")
    def test_health_check(self, mock_httpx: Mock):
        """Test health check functionality."""
        # Mock successful responses
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None

        mock_client = Mock()
        mock_client.get.return_value = mock_response
        mock_httpx.return_value = mock_client

        client = LLMClient()
        health = client.health_check()

        assert "openrouter" in health
        assert "ollama" in health

    @patch("banana_forge.llm.httpx.Client")
    def test_health_check_with_failures(self, mock_httpx: Mock):
        """Test health check with service failures."""
        # Mock failed responses
        mock_client = Mock()
        mock_client.get.side_effect = httpx.RequestError("Connection failed")
        mock_httpx.return_value = mock_client

        client = LLMClient()
        health = client.health_check()

        assert "openrouter" in health
        assert "ollama" in health
        assert health["openrouter"] is False
        assert health["ollama"] is False

    @patch("banana_forge.llm.httpx.Client")
    def test_list_available_models(self, mock_httpx: Mock):
        """Test listing available models."""
        mock_client = Mock()
        mock_httpx.return_value = mock_client

        client = LLMClient()
        models = client.list_available_models()

        assert "openrouter" in models
        assert "ollama" in models
        assert isinstance(models["openrouter"], list)
        assert isinstance(models["ollama"], list)

    def test_generate_completion_error_handling(self):
        """Test error handling in completion generation."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_openrouter_completion") as mock_gen:
                mock_gen.side_effect = Exception("Network error")

                client = LLMClient()

                with pytest.raises(Exception):
                    client.generate_completion(
                        prompt="Test prompt", model="openai/gpt-4", max_tokens=100
                    )

    def test_model_routing(self):
        """Test that models are routed to correct providers."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_openrouter_completion") as mock_or:
                with patch.object(LLMClient, "_generate_ollama_completion") as mock_ol:
                    client = LLMClient()

                    # Test OpenRouter routing
                    mock_or.return_value = "OpenRouter response"
                    result = client.generate_completion("test", "openai/gpt-4")
                    assert result == "OpenRouter response"
                    mock_or.assert_called_once()

                    # Test Ollama routing
                    mock_ol.return_value = "Ollama response"
                    result = client.generate_completion("test", "qwen2.5:8b")
                    assert result == "Ollama response"
                    mock_ol.assert_called_once()

    @patch("banana_forge.llm.OpenAI")
    def test_generate_openrouter_completion_success(self, mock_openai):
        """Test successful OpenRouter completion generation."""
        # Mock the OpenAI client and response
        mock_client = MagicMock()
        mock_openai.return_value = mock_client

        # Create a mock response with proper structure
        mock_choice = MagicMock()
        mock_choice.message.content = "Generated response from OpenRouter"
        mock_response = MagicMock()
        mock_response.choices = [mock_choice]
        mock_response.usage = MagicMock()
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 20

        mock_client.chat.completions.create.return_value = mock_response

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = "test-key"
            mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = True

            client = LLMClient()
            result = client._generate_openrouter_completion(
                prompt="Test prompt",
                model="openai/gpt-4",
                max_tokens=100,
                temperature=0.7
            )

            assert result == "Generated response from OpenRouter"
            mock_client.chat.completions.create.assert_called_once()

    @patch("banana_forge.llm.OpenAI")
    def test_generate_openrouter_completion_no_client(self, mock_openai):
        """Test OpenRouter completion when client is not available."""
        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None  # No API key
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(ValueError, match="OpenRouter client not available"):
                client._generate_openrouter_completion(
                    prompt="Test prompt",
                    model="openai/gpt-4",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.OpenAI")
    def test_generate_openrouter_completion_empty_response(self, mock_openai):
        """Test OpenRouter completion with empty response."""
        mock_client = MagicMock()
        mock_openai.return_value = mock_client

        # Mock empty choices
        mock_response = MagicMock()
        mock_response.choices = []
        mock_client.chat.completions.create.return_value = mock_response

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = "test-key"
            mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(ValueError, match="No choices returned from OpenRouter"):
                client._generate_openrouter_completion(
                    prompt="Test prompt",
                    model="openai/gpt-4",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.OpenAI")
    def test_generate_openrouter_completion_invalid_content(self, mock_openai):
        """Test OpenRouter completion with invalid content."""
        mock_client = MagicMock()
        mock_openai.return_value = mock_client

        # Mock response with empty content
        mock_choice = MagicMock()
        mock_choice.message.content = ""  # Empty content
        mock_response = MagicMock()
        mock_response.choices = [mock_choice]
        mock_client.chat.completions.create.return_value = mock_response

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = "test-key"
            mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(ValueError, match="Empty or invalid response from OpenRouter"):
                client._generate_openrouter_completion(
                    prompt="Test prompt",
                    model="openai/gpt-4",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.httpx.Client")
    def test_generate_ollama_completion_success(self, mock_httpx):
        """Test successful Ollama completion generation."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock successful response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "response": "Generated response from Ollama",
            "eval_count": 25
        }
        mock_response.raise_for_status.return_value = None
        mock_client.post.return_value = mock_response

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = True

            client = LLMClient()
            result = client._generate_ollama_completion(
                prompt="Test prompt",
                model="qwen2.5:8b",
                max_tokens=100,
                temperature=0.7
            )

            assert result == "Generated response from Ollama"
            mock_client.post.assert_called_once_with(
                "/api/generate",
                json={
                    "model": "qwen2.5:8b",
                    "prompt": "Test prompt",
                    "stream": False,
                    "options": {
                        "num_predict": 100,
                        "temperature": 0.7
                    }
                }
            )

    @patch("banana_forge.llm.httpx.Client")
    def test_generate_ollama_completion_empty_response(self, mock_httpx):
        """Test Ollama completion with empty response."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock empty response
        mock_response = MagicMock()
        mock_response.json.return_value = {"response": ""}
        mock_response.raise_for_status.return_value = None
        mock_client.post.return_value = mock_response

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(ValueError, match="Empty response from Ollama"):
                client._generate_ollama_completion(
                    prompt="Test prompt",
                    model="qwen2.5:8b",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.httpx.Client")
    def test_generate_ollama_completion_404_error(self, mock_httpx):
        """Test Ollama completion with 404 model not found error."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock 404 error
        mock_response = MagicMock()
        mock_response.status_code = 404
        error = httpx.HTTPStatusError("Model not found", request=MagicMock(), response=mock_response)
        mock_client.post.side_effect = error

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(ValueError, match="Model 'qwen2.5:8b' not found in Ollama"):
                client._generate_ollama_completion(
                    prompt="Test prompt",
                    model="qwen2.5:8b",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.httpx.Client")
    def test_generate_ollama_completion_http_error(self, mock_httpx):
        """Test Ollama completion with HTTP error."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock 500 error
        mock_response = MagicMock()
        mock_response.status_code = 500
        error = httpx.HTTPStatusError("Server error", request=MagicMock(), response=mock_response)
        mock_client.post.side_effect = error

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(httpx.HTTPStatusError):
                client._generate_ollama_completion(
                    prompt="Test prompt",
                    model="qwen2.5:8b",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.httpx.Client")
    def test_generate_ollama_completion_general_error(self, mock_httpx):
        """Test Ollama completion with general error."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock general error
        mock_client.post.side_effect = Exception("Connection failed")

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()

            with pytest.raises(Exception, match="Connection failed"):
                client._generate_ollama_completion(
                    prompt="Test prompt",
                    model="qwen2.5:8b",
                    max_tokens=100,
                    temperature=0.7
                )

    @patch("banana_forge.llm.httpx.Client")
    def test_list_available_models_success(self, mock_httpx):
        """Test successful listing of available models."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock Ollama models response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "models": [
                {"name": "qwen2.5:8b"},
                {"name": "llama2:7b"}
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_client.get.return_value = mock_response

        with patch("banana_forge.llm.OpenAI") as mock_openai:
            with patch("banana_forge.llm.settings") as mock_settings:
                mock_settings.openrouter_api_key = "test-key"
                mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
                mock_settings.ollama_base_url = "http://localhost:11434"
                mock_settings.verbose = False

                client = LLMClient()
                models = client.list_available_models()

                assert "openrouter" in models
                assert "ollama" in models
                assert "qwen2.5:8b" in models["ollama"]
                assert "llama2:7b" in models["ollama"]
                assert "moonshot/kimi-k2" in models["openrouter"]

    @patch("banana_forge.llm.httpx.Client")
    def test_list_available_models_ollama_error(self, mock_httpx):
        """Test listing models when Ollama fails."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client

        # Mock Ollama error
        mock_client.get.side_effect = Exception("Connection failed")

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()
            models = client.list_available_models()

            assert "openrouter" in models
            assert "ollama" in models
            assert models["ollama"] == []  # Should be empty due to error

    @patch("banana_forge.llm.OpenAI")
    @patch("banana_forge.llm.httpx.Client")
    def test_health_check_success(self, mock_httpx, mock_openai):
        """Test successful health check for both providers."""
        # Mock Ollama client
        mock_ollama_client = MagicMock()
        mock_httpx.return_value = mock_ollama_client
        mock_ollama_response = MagicMock()
        mock_ollama_response.status_code = 200
        mock_ollama_client.get.return_value = mock_ollama_response

        # Mock OpenRouter client
        mock_openrouter_client = MagicMock()
        mock_openai.return_value = mock_openrouter_client
        mock_openrouter_client.chat.completions.create.return_value = MagicMock()

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = "test-key"
            mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.primary_model = "openai/gpt-4"
            mock_settings.verbose = False

            client = LLMClient()
            health = client.health_check()

            assert health["openrouter"] is True
            assert health["ollama"] is True

    @patch("banana_forge.llm.OpenAI")
    @patch("banana_forge.llm.httpx.Client")
    def test_health_check_openrouter_failure(self, mock_httpx, mock_openai):
        """Test health check with OpenRouter failure."""
        # Mock Ollama client (success)
        mock_ollama_client = MagicMock()
        mock_httpx.return_value = mock_ollama_client
        mock_ollama_response = MagicMock()
        mock_ollama_response.status_code = 200
        mock_ollama_client.get.return_value = mock_ollama_response

        # Mock OpenRouter client (failure)
        mock_openrouter_client = MagicMock()
        mock_openai.return_value = mock_openrouter_client
        mock_openrouter_client.chat.completions.create.side_effect = Exception("API Error")

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = "test-key"
            mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.primary_model = "openai/gpt-4"
            mock_settings.verbose = False

            client = LLMClient()
            health = client.health_check()

            assert health["openrouter"] is False
            assert health["ollama"] is True

    @patch("banana_forge.llm.httpx.Client")
    def test_health_check_ollama_failure(self, mock_httpx):
        """Test health check with Ollama failure."""
        mock_client = MagicMock()
        mock_httpx.return_value = mock_client
        mock_client.get.side_effect = Exception("Connection failed")

        with patch("banana_forge.llm.settings") as mock_settings:
            mock_settings.openrouter_api_key = None  # No OpenRouter
            mock_settings.ollama_base_url = "http://localhost:11434"
            mock_settings.verbose = False

            client = LLMClient()
            health = client.health_check()

            assert health["openrouter"] is False
            assert health["ollama"] is False

    @patch("banana_forge.llm.settings")
    def test_setup_clients_verbose_logging(self, mock_settings):
        """Test client setup with verbose logging enabled."""
        mock_settings.openrouter_api_key = "test-key"
        mock_settings.openrouter_base_url = "https://openrouter.ai/api/v1"
        mock_settings.ollama_base_url = "http://localhost:11434"
        mock_settings.verbose = True

        with patch("banana_forge.llm.OpenAI") as mock_openai:
            with patch("banana_forge.llm.httpx.Client") as mock_httpx:
                with patch("banana_forge.llm.logger") as mock_logger:
                    client = LLMClient()

                    # Verify verbose logging was called
                    mock_logger.info.assert_any_call("OpenRouter client initialized")
                    mock_logger.info.assert_any_call("Ollama client initialized for http://localhost:11434")

    @patch("banana_forge.llm.settings")
    def test_setup_clients_no_api_key_warning(self, mock_settings):
        """Test client setup warning when no API key is provided."""
        mock_settings.openrouter_api_key = None  # No API key
        mock_settings.ollama_base_url = "http://localhost:11434"
        mock_settings.verbose = False

        with patch("banana_forge.llm.httpx.Client") as mock_httpx:
            with patch("banana_forge.llm.logger") as mock_logger:
                client = LLMClient()

                # Verify warning was logged
                mock_logger.warning.assert_called_with(
                    "OpenRouter API key not provided - remote models unavailable"
                )

    def test_verbose_logging_in_generate_completion(self):
        """Test verbose logging in generate_completion method."""
        with patch.object(LLMClient, "_setup_clients"):
            with patch.object(LLMClient, "_generate_ollama_completion") as mock_gen:
                with patch("banana_forge.llm.settings") as mock_settings:
                    with patch("banana_forge.llm.logger") as mock_logger:
                        mock_settings.verbose = True
                        mock_gen.return_value = "Test response"

                        client = LLMClient()
                        client.generate_completion("Test prompt", "qwen2.5:8b")

                        # Verify verbose logging
                        mock_logger.info.assert_any_call("Generating completion with model: qwen2.5:8b")
                        mock_logger.debug.assert_any_call("Prompt length: 11 characters")


if __name__ == "__main__":
    pytest.main([__file__])
